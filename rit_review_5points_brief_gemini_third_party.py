import os
import pathlib
import logging
import pandas as pd # Not directly used in the provided snippet but keeping it
import csv
import re
from dotenv import load_dotenv

# Langchain imports for OpenAI-compatible API
from langchain_openai import ChatOpenAI # Changed from langchain_google_genai
from langchain.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser
from langchain_community.document_loaders import PyPDFLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter

# --- Setup basic logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)
logging.info("--- LOGGER INITIALIZED --- This message confirms basicConfig has run and force=True was used (requires Python 3.8+).")

# --- Configuration ---
# MODEL_NAME = "gemini-2.5-pro"  # Original Google Gemini model
MODEL_NAME = "google/gemini-2.5-pro" # Using a common OpenAI-compatible model. Adjust if your API uses a different name.

# --- New: OpenAI-compatible API Configuration ---
# IMPORTANT: Replace with the actual base URL of your third-party OpenAI-compatible API.
# Examples:
#   - For a local LiteLLM proxy: "http://localhost:4000/v1"
#   - For other custom endpoints: "https://your-custom-api.com/v1"
OPENAI_COMPATIBLE_BASE_URL_ENV_VAR = "OPENAI_API_BASE" # Environment variable name for the base URL
OPENAI_API_KEY_ENV_VAR = "OPENAI_API_KEY" # Environment variable name for the API key

def load_api_config():
    """Loads OpenAI-compatible API key and base URL from .env file or environment variables."""
    load_dotenv()
    api_key = os.getenv(OPENAI_API_KEY_ENV_VAR)
    base_url = os.getenv(OPENAI_COMPATIBLE_BASE_URL_ENV_VAR)

    if not api_key:
        logging.warning(f"{OPENAI_API_KEY_ENV_VAR} not found. Using a dummy key. Ensure it's set if your API requires it.")
        api_key = "sk-dummykey" # Provide a dummy key if not found, useful for some local proxies

    if not base_url:
        logging.error(f"{OPENAI_COMPATIBLE_BASE_URL_ENV_VAR} not found. Please set it in environment variables or a .env file.")
        raise ValueError(f"{OPENAI_COMPATIBLE_BASE_URL_ENV_VAR} not found. This is critical for connecting to your third-party API.")
    
    return api_key, base_url

def load_criteria_questions() -> list:
    """
    Loads criteria questions and details from the provided CSV file and the prompt.
    The CSV is primarily used for the 'criterion_short_name'.
    """

    # The 12 questions and their detailed scoring instructions (as provided by the user)
    # output_field_name is used to customize the prompt for the specific detail line.
    questions_data = [
        {
            "id": 1,
            "criterion_short_name":  "Qualitative Methodology",
            "question_text": "Does this paper explicitly described the particular qualitative methodology used in the study (e.g., action research, case study, grounded theory)?",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Research Strategy"
        },
        {
            "id": 2,
            "criterion_short_name":  "Study Milieu",
            "question_text": "Does this paper explicitly described the physical, social, and cultural milieu of the study (e.g., firm conditions, industry, participants' social status)?",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Study Milieu Context"
        },
        {
            "id": 3,
            "criterion_short_name":  "Researcher Relationship",
            "question_text": "Does this paper explicitly described The researcher's relationship with the organization and study participants; the closer the relationship, the more the researcher is an insider rather than an outsider?",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Researcher Relationship Details"
        },
        {
            "id": 4,
            "criterion_short_name":  "Participant Selection",
            "question_text": "Does this paper explicitly described how participants , interviewees or cases are selected for the study. .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Participant Selection Method"
        },
        {
            "id": 5,
            "criterion_short_name":  "Participant Importance",
            "question_text": "Does this paper explicitly described Relative importance of the participants/cases for the study, that is The study's sample and the relative importance of each participant or case .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Participant Importance Details"
        },
        {
            "id": 6,
            "criterion_short_name":  "Interaction Documentation",
            "question_text": "Does this paper explicitly Documenting interactions with participants for the study, The documentation and transcription of the interviews and all other forms of observations (e.g., audio, video, notations) .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Interaction Documentation Method"
        },
        {
            "id": 7,
            "criterion_short_name":  "Saturation Point",
            "question_text": "Does this paper explicitly Saturation point a Saturation point for the data collection, which occurs when there are no new insights or themes in the process of collecting data and drawing conclusions .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Saturation Point Details"
        },
        {
            "id": 8,
            "criterion_short_name":  "Unexpected Events",
            "question_text": "Does this paper explicitly mentioned Unexpected opportunities, challenges, and other events. Unexpected opportunities (e.g., access to additional sources of data), challenges (e.g., a firm's unit declines to participate in the last data collection stage and is replaced by a different one), and events (e.g., internal and external changes such as a new CEO or changes in market conditions during the study) that occur during all stages of the research process .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Unexpected Events Management"
        },
        {
            "id": 9,
            "criterion_short_name":  "Power Imbalance Management",
            "question_text": "Does this paper explicitly mention the Management of power imbalance for the data collection, that is The differential exercise of control, authority, or influence during the research process .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Power Imbalance Management Details"
        },
        {
            "id": 10,
            "criterion_short_name":  "Data Coding & First-Order Codes",
            "question_text": "In the data analysis phase, does this paper explicitly mention the Data coding and first-order codes , the process through which data are categorized to facilitate subsequent analysis (e.g., structural coding, descriptive coding, narrative coding) .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "First-Order Coding Process"
        },
        {
            "id": 11,
            "criterion_short_name":  "Data Analysis & Higher-Order Codes",
            "question_text": "In the data analysis phase, does this paper explicitly mention the Data analysis and second- and higher-order codes , The classification and interpretation of linguistic or visual material to make statements about implicit and explicit dimensions and structures and it is generally done by identifying key relationships that tie the first order codes together into a narrative or sequence . If there is no first order codes, there shouldn't be any second order codes.",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Higher-Order Coding Process"
        },
        {
            "id": 12,
            "criterion_short_name":  "Data Disclosure",
            "question_text": "Does this paper explicitly mention the Data disclosure, that is ensure that the materials needed to replicate research are available .",
            "scoring_instructions": "1 points for Not at all transparent: No procedures or choices are described, 2 points for Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear, 3 points for Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed, 4 points for Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain, 5 points for Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study.",
            "output_field_name": "Data Disclosure Statement"
        }
    ]

    return questions_data

def load_pdf_content(pdf_path: str) -> str:
    """Load and extract text content from a PDF file."""
    try:
        loader = PyPDFLoader(pdf_path)
        documents = loader.load()
        
        # Combine all pages into a single text
        full_text = "\n\n".join([doc.page_content for doc in documents])
        logging.info(f"Successfully loaded PDF with {len(documents)} pages")
        return full_text
    except Exception as e:
        logging.error(f"Error loading PDF {pdf_path}: {e}")
        raise


class RITReviewerAssistant:
    def __init__(self, pdf_path: str, api_key: str, base_url: str, criteria_questions: list): # Modified parameters
        self.pdf_path = pdf_path
        self.api_key = api_key # Changed from google_api_key
        self.base_url = base_url # New parameter for base URL
        self.criteria_questions = criteria_questions
        
        # Initialize with ChatOpenAI
        self.llm = ChatOpenAI(
            model=MODEL_NAME, 
            temperature=0, 
            openai_api_key=self.api_key, # Use the passed API key
            openai_api_base=self.base_url # Use the passed base URL
        )
        self.pdf_content = None
        self._load_pdf_content()

    def _load_pdf_content(self):
        """Load the PDF content."""
        if not pathlib.Path(self.pdf_path).exists():
            logging.error(f"PDF file not found at: {self.pdf_path}")
            raise FileNotFoundError(f"PDF file not found at: {self.pdf_path}")

        logging.info(f"Loading PDF content from: {self.pdf_path}")
        try:
            self.pdf_content = load_pdf_content(self.pdf_path)
            logging.info("PDF content loaded successfully.")
        except Exception as e:
            logging.error(f"Could not load PDF content from {self.pdf_path}: {e}", exc_info=True)
            raise

    def parse_review_output(self, review_text):
        """
        Parses the LLM output to extract Point, specified_details, and Where.
        Returns a dict with keys: point, specified_details, where.
        """
        # Use regex to extract the required fields
        point_match = re.search(r"Point:\s*(\d+)", review_text)
        # details_match = re.search(r":\s*(.+)", review_text) # This regex was problematic for generic details
        where_match = re.search(r"Where:\s*(.+)", review_text)

        # Try to get the output_field_name from the review_text
        details_field = ""
        details_value = ""
        # Improved parsing for specified_details, assuming it's the line between "Point:" and "Where:"
        # or the first line after "Point:" that contains a colon.
        lines = review_text.splitlines()
        point_found = False
        for line in lines:
            if line.startswith("Point:"):
                point_found = True
                continue
            if point_found and line.strip() and not line.startswith("Where:"):
                if ":" in line:
                    details_field, details_value = line.split(":", 1)
                    details_value = details_value.strip()
                    break
                else: # If no colon, just take the whole line as the value, adjust field if necessary
                    details_field = "specified_details" # Default field name
                    details_value = line.strip()
                    break


        return {
            "point": point_match.group(1) if point_match else "",
            "specified_details": details_value,
            "where": where_match.group(1) if where_match else ""
        }

    def _parse_combined_review_output(self, combined_review_text):
        """
        Parses the combined LLM output to extract reviews for all criteria.
        Returns tuple: (all_results, csv_rows)
        """
        all_results = []
        csv_rows = []

        # Split the text by criterion sections
        criterion_sections = re.split(r'=== CRITERION (\d+):', combined_review_text)

        # The first element is usually empty or contains preamble text
        if len(criterion_sections) > 1:
            # Process each criterion section (skip the first empty element)
            for i in range(1, len(criterion_sections), 2):
                if i + 1 < len(criterion_sections):
                    criterion_id = criterion_sections[i].strip()
                    criterion_content = criterion_sections[i + 1]

                    # Find the corresponding question data
                    q_data = None
                    for question in self.criteria_questions:
                        if str(question['id']) == criterion_id:
                            q_data = question
                            break

                    if q_data:
                        # Parse this criterion's content
                        parsed = self.parse_review_output(f"Point: {criterion_content}")

                        all_results.append({
                            "criterion_id": q_data['id'],
                            "criterion_name": q_data['criterion_short_name'],
                            "review": criterion_content.strip()
                        })

                        csv_rows.append({
                            "question_id": q_data["id"],
                            "point": parsed["point"],
                            "specified_details": parsed["specified_details"],
                            "where": parsed["where"]
                        })

                        print(f"--- Criterion {q_data['id']}: {q_data['criterion_short_name']} ---")
                        print(criterion_content.strip())
                        print("\n")

        # If parsing failed, try alternative approach
        if not all_results:
            logging.warning("Primary parsing method failed, trying alternative approach...")
            # Fallback: try to find individual criterion responses in the text
            for q_data in self.criteria_questions:
                # Look for patterns like "CRITERION 1:" or "Criterion 1:"
                pattern = rf"(?i)criterion\s+{q_data['id']}[:\s].*?(?=criterion\s+\d+|$)"
                match = re.search(pattern, combined_review_text, re.DOTALL)

                if match:
                    criterion_content = match.group(0)
                    parsed = self.parse_review_output(criterion_content)

                    all_results.append({
                        "criterion_id": q_data['id'],
                        "criterion_name": q_data['criterion_short_name'],
                        "review": criterion_content.strip()
                    })

                    csv_rows.append({
                        "question_id": q_data["id"],
                        "point": parsed["point"],
                        "specified_details": parsed["specified_details"],
                        "where": parsed["where"]
                    })
                else:
                    # If still no match, create empty entry
                    all_results.append({
                        "criterion_id": q_data['id'],
                        "criterion_name": q_data['criterion_short_name'],
                        "review": "Could not parse response for this criterion."
                    })

                    csv_rows.append({
                        "question_id": q_data["id"],
                        "point": "",
                        "specified_details": "",
                        "where": ""
                    })

        return all_results, csv_rows

    def review_paper(self, csv_output_path=None):
        """Reviews the PDF content using OpenAI-compatible LLM and generates reviews for ALL criteria in a single request.
        Optionally writes results to a CSV file.
        """
        if not self.pdf_content:
            logging.error("PDF content not loaded. Cannot review paper.")
            return

        # Build all criteria questions into a single prompt
        criteria_text = ""
        for q_data in self.criteria_questions:
            criteria_text += f"""
CRITERION {q_data['id']}: {q_data['criterion_short_name']}
Question: {q_data['question_text']}
Scoring Instructions: {q_data['scoring_instructions']}
Output Field Name: {q_data['output_field_name']}

"""

        # Create the prompt template for all criteria at once
        prompt_template = ChatPromptTemplate.from_messages([
            ("system", """You are a meticulous RIT (Research Transparency Index) reviewer. Your expertise is in evaluating research papers against specific transparency criteria.
You will be provided with the full text of a research paper and ALL 12 criteria to evaluate.
Analyze the paper text thoroughly in relation to each criterion and its scoring instructions.

For EACH criterion, your answer MUST strictly follow this format:

=== CRITERION [ID]: [Short Name] ===
Point: <1, 2, 3, 4, or 5>
[Output Field Name]: <Specific details found that justify the score, e.g., "action research", "firm conditions and participant status described on page X", "saturation explicitly stated and justified in methods section">
Where: <Location in the paper, e.g., "Methodology section, paragraph 3", "Introduction, page 2, first paragraph", "Appendix A", "Not found">

Scoring Guidelines for all criteria:
- 1 points: Not at all transparent: No procedures or choices are described. For "Where:", state "Not found".
- 2 points: Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear. For "Where:", specify the location as precisely as possible from the text.
- 3 points: Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed. For "Where:", specify the location as precisely as possible from the text.
- 4 points: Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain. For "Where:", specify the location as precisely as possible from the text.
- 5 points: Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study. For "Where:", specify the location as precisely as possible from the text.

Critically evaluate the provided paper text. Do not infer information that is not present.
If the text is insufficient to make a clear determination for 2, 3, 4, or 5 points, err on the side of a lower score.
Evaluate ALL 12 criteria and provide responses for each one."""),
            ("human", """Full text of the research paper:
-------
{paper_text}
-------

Please evaluate this research paper against ALL of the following criteria:

{all_criteria}

Based on the provided paper text, please provide your review for ALL criteria following the specified format.""")
        ])

        # Create the chain
        chain = prompt_template | self.llm | StrOutputParser()

        print(f"\n--- RIT Review ---")
        print(f"--- PDF: {self.pdf_path} ---")
        print(f"--- Processing ALL {len(self.criteria_questions)} criteria in a single request ---\n")

        try:
            # Single API call for all criteria
            logging.info("Sending single request for all criteria...")
            result = chain.invoke({
                "paper_text": self.pdf_content,
                "all_criteria": criteria_text
            })

            print("=== COMPLETE REVIEW RESULTS ===")
            print(result)
            print("\n")

            # Parse the combined result
            all_results, csv_rows = self._parse_combined_review_output(result)

        except Exception as e:
            logging.error(f"Error during combined review: {e}", exc_info=True)
            print("Error: Could not generate review for the criteria.")
            # Create empty results for all criteria
            all_results = []
            csv_rows = []
            for q_data in self.criteria_questions:
                all_results.append({"criterion_id": q_data['id'], "criterion_name": q_data['criterion_short_name'], "review": "Error during review."})
                csv_rows.append({
                    "question_id": q_data["id"],
                    "point": "",
                    "specified_details": "",
                    "where": ""
                })

        if csv_output_path:
            with open(csv_output_path, "w", newline='', encoding="utf-8") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=["question_id", "point", "specified_details", "where"])
                writer.writeheader()
                writer.writerows(csv_rows)
            print(f"\nCSV output saved to: {csv_output_path}")

        return all_results

if __name__ == "__main__":
    try:
        # Load API key and base URL for the OpenAI-compatible API
        api_key, base_url = load_api_config() # Changed function call
        current_dir = pathlib.Path(__file__).parent.resolve()

        # Configure the PDF to process
        pdf_name = "VT1-7-AMJ-Jones"
        pdf_path = str(current_dir / "data" / f"{pdf_name}.pdf")

        # Check if PDF exists
        if not pathlib.Path(pdf_path).exists():
            # Try alternative location
            pdf_path = str(current_dir / "sample_pdfs_for_docling_script" / f"{pdf_name}.pdf")
            if not pathlib.Path(pdf_path).exists():
                logging.error(f"PDF file not found at: {pdf_path}")
                print(f"ERROR: PDF file not found. Please ensure '{pdf_name}.pdf' exists in either 'data/' or 'sample_pdfs_for_docling_script/' directory.")
                exit(1)

        # Updated CSV output filename to reflect the change
        csv_output_path = str(current_dir / "rit_review_output" / f"rit_review_{pdf_name}_gemini.csv")

        # Ensure output directory exists
        pathlib.Path(csv_output_path).parent.mkdir(parents=True, exist_ok=True)

        questions = load_criteria_questions()
        if not questions:
            logging.error("No questions loaded. Exiting.")
        else:
            assistant = RITReviewerAssistant(
                pdf_path=pdf_path,
                api_key=api_key, # Pass the loaded API key
                base_url=base_url, # Pass the loaded base URL
                criteria_questions=questions
            )
            assistant.review_paper(csv_output_path=csv_output_path)

    except ValueError as ve:
        print(f"Configuration Error: {ve}")
    except FileNotFoundError as fnfe:
        print(f"File Not Found Error: {fnfe}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in main: {e}", exc_info=True)
        print(f"An unexpected error occurred: {e}")