import os
import pathlib
import logging
import pandas as pd
import csv
import re
from dotenv import load_dotenv

# Langchain imports
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.vectorstores import FAISS
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser

# --- Setup basic logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)
logging.info("--- LOGGER INITIALIZED --- This message confirms basicConfig has run and force=True was used (requires Python 3.8+).")


# --- Configuration ---
MODEL_NAME = "gpt-4o-mini" # 

def load_api_key():
    """Loads OpenAI API key from .env file or environment variables."""
    load_dotenv()
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY not found. Please set it in environment variables or a .env file.")
        raise ValueError("OPENAI_API_KEY not found.")
    return api_key

def load_criteria_questions() -> list:
    """
    Loads criteria questions and details from the provided CSV file and the prompt.
    The CSV is primarily used for the 'criterion_short_name'.
    """

    # The 12 questions and their detailed scoring instructions (as provided by the user)
    # output_field_name is used to customize the prompt for the specific detail line.
    questions_data = [
        {
            "id": 1,
            "criterion_short_name":  "Qualitative Methodology",
            "question_text": "Does this paper explicitly described the particular qualitative methodology used in the study (e.g., action research, case study, grounded theory)?",
            "scoring_instructions": """1 points - Not at all transparent No mention of research method or approach Does not indicate that qualitative methods were used Might only say "conducted a study" or "research was done”; 2 points - Slightly transparent Mentions "qualitative study" or "qualitative research" in general terms No specification of particular qualitative methodology Does not name specific approach (e.g., case study, grounded theory) ;3 points  - Moderately transparent Names the specific qualitative methodology used (e.g., "case study," "grounded theory," "phenomenology") May include brief description of what this method involves Limited or no explanation of why this method was chosen; 4 points  - Transparent Clear identification and basic explanation of the qualitative methodology Provides rationale for why this method was appropriate for the research question Some discussion of how the method influenced key study decisions; 5 points - Fully transparent Comprehensive description of the qualitative methodology and its application Clear justification for method selection with consideration of alternatives Discusses how the method's assumptions and characteristics influenced the study design, data collection, and analysis approach""",
            "output_field_name": "Research Strategy"
        },
        {
            "id": 2,
            "criterion_short_name":  "Study Milieu",
            "question_text": "Does this paper explicitly described the physical, social, and cultural milieu of the study (e.g., firm conditions, industry, participants' social status)?",
            "scoring_instructions": """1 points - Not at all transparent No mention of where the research took place Cannot determine the research context or location; 2 points - Slightly transparent Very basic identification of setting (e.g., "a company," "an organization") No specific details about the context; 3 points - Moderately transparent Names the type of organization or industry (e.g., "manufacturing company," "healthcare organization") Provides some basic contextual information Limited description of relevant setting characteristics; 4 points - Transparent Clear description of the research setting including organizational and industry context Explains relevant environmental factors that might affect the research Provides sufficient context for readers to understand the setting; 5 points - Fully transparent Comprehensive description of the physical, social, and cultural context Rich detail about organizational characteristics, industry conditions, and environmental factors Thorough explanation of how setting characteristics influenced the research process and findings """,
            "output_field_name": "Study Milieu Context"
        },
        {
            "id": 3,
            "criterion_short_name":  "Researcher Relationship",
            "question_text": "Does this paper explicitly described The researcher's relationship with the organization and study participants; the closer the relationship, the more the researcher is an insider rather than an outsider?",
            "scoring_instructions": """1 points - Not at all transparent No information about the researcher's role or relationship to the study context Cannot determine how the researcher accessed the research site; 2 points - Slightly transparent Basic mention of researcher role (e.g., "researcher," "investigator") No explanation of relationship to organization or participants; 3 points - Moderately transparent Indicates researcher position (e.g., "external researcher," "consultant," "employee") Some information about access to the organization Limited discussion of researcher-participant relationship; 4 points - Transparent Clear description of researcher's relationship to the organization and participants Explains how this position affected access to data and information Discusses potential effects on participant responses; 5 points - Fully transparent Comprehensive explanation of researcher positioning and relationship dynamics Detailed discussion of how insider/outsider status influenced data accessibility and participant disclosure Thorough reflection on how researcher position affected information interpretation """,
            "output_field_name": "Researcher Relationship Details"
        },
        {
            "id": 4,
            "criterion_short_name":  "Participant Selection",
            "question_text": "Does this paper explicitly described how participants , interviewees or cases are selected for the study. .",
            "scoring_instructions": """1 points - Not at all transparent No explanation of how participants were selected Cannot determine any selection process or criteria; 2 points - Slightly transparent Basic mention that participants were selected but no details May mention number of participants without explaining selection; 3 points - Moderately transparent Names sampling approach (e.g., "purposive sampling," "convenience sampling") Provides basic selection criteria or target characteristics Some explanation of participant recruitment; 4 points - Transparent Clear description of sampling procedures and selection criteria Explains rationale for sampling approach Discusses what type of variability was sought; 5 points - Fully transparent Comprehensive documentation of sampling strategy and procedures Detailed explanation of selection criteria and rationale Thorough discussion of desired variability, specific dimensions, and potential sampling limitations """,
            "output_field_name": "Participant Selection Method"
        },
        {
            "id": 5,
            "criterion_short_name":  "Participant Importance",
            "question_text": "Does this paper explicitly described Relative importance of the participants/cases for the study, that is The study's sample and the relative importance of each participant or case .",
            "scoring_instructions": """1 points - Not at all transparent No description of participants beyond total number Cannot identify any participant characteristics; 2 points - Slightly transparent Basic demographic information (e.g., "managers and employees") No details about individual participants or their roles; 3 points - Moderately transparent Provides some participant characteristics (e.g., roles, experience levels) Basic description that allows some understanding of sample composition Limited information about participant diversity; 4 points - Transparent Clear description of participant characteristics and backgrounds Explains different participant roles and their relevance to the study Sufficient detail for readers to understand sample composition; 5 points - Fully transparent Comprehensive characterization of all participants with detailed profiles Clear explanation of each participant's contribution and importance to findings Rich detail enabling assessment of sample diversity and representativeness """, 
            "output_field_name": "Participant Importance Details"
        },
        {
            "id": 6,
            "criterion_short_name":  "Interaction Documentation",
            "question_text": "Does this paper explicitly Documenting interactions with participants for the study, The documentation and transcription of the interviews and all other forms of observations (e.g., audio, video, notations) .",
            "scoring_instructions": """1 points - Not at all transparent No information about how data were collected or recorded Cannot determine documentation methods; 2 points - Slightly transparent Basic mention of data collection activity (e.g., "interviews conducted") No details about recording or documentation procedures; 3 points - Moderately transparent Indicates documentation method (e.g., "audio recorded," "notes taken") Some basic information about data collection procedures Limited detail about transcription or processing; 4 points - Transparent Clear description of documentation methods and transcription procedures Explains quality assurance measures for data recording Some consideration of how recording methods might affect participants; 5 points - Fully transparent Comprehensive description of all documentation and transcription procedures Detailed explanation of quality controls and data processing methods Thorough consideration of how documentation methods affected participant behavior and information sharing """,
            "output_field_name": "Interaction Documentation Method"
        },
        {
            "id": 7,
            "criterion_short_name":  "Saturation Point",
            "question_text": "Does this paper explicitly Saturation point a Saturation point for the data collection, which occurs when there are no new insights or themes in the process of collecting data and drawing conclusions .",
            "scoring_instructions": """1 points - Not at all transparent No mention of when or why data collection ended Cannot determine data collection stopping criteria; 2 points - Slightly transparent May mention "saturation" briefly without explanation No description of how adequacy of data was determined; 3 points - Moderately transparent Mentions reaching saturation or data adequacy Basic indication of when data collection was considered sufficient Limited explanation of saturation criteria; 4 points - Transparent Clear explanation of when saturation was reached Describes criteria used to determine data adequacy Some discussion of judgment calls in the saturation decision; 5 points - Fully transparent Comprehensive explanation of saturation monitoring and determination Detailed description of specific indicators and decision criteria Thorough discussion of how researchers judged that no new insights or themes were emerging """, 
            "output_field_name": "Saturation Point Details"
        },
        {
            "id": 8,
            "criterion_short_name":  "Unexpected Events",
            "question_text": "Does this paper explicitly mentioned Unexpected opportunities, challenges, and other events. Unexpected opportunities (e.g., access to additional sources of data), challenges (e.g., a firm's unit declines to participate in the last data collection stage and is replaced by a different one), and events (e.g., internal and external changes such as a new CEO or changes in market conditions during the study) that occur during all stages of the research process .",
            "scoring_instructions": """1 points - Not at all transparent No mention of any challenges, changes, or unexpected events Research appears to have proceeded exactly as planned; 2 points - Slightly transparent Brief acknowledgment that some challenges or changes occurred No specific details about events or how they were handled; 3 points - Moderately transparent Mentions some specific challenges, opportunities, or unexpected events Basic description of what occurred during the research Limited explanation of researcher responses; 4 points - Transparent Clear description of unexpected events and how researchers responded Explains adaptations made to the research process Discusses impact of these events on data collection or analysis; 5 points - Fully transparent Comprehensive account of unexpected opportunities, challenges, and events Detailed explanation of researcher responses and adaptation strategies Thorough analysis of how these events affected the research process and conclusions """, 
            "output_field_name": "Unexpected Events Management"
        },
        {
            "id": 9,
            "criterion_short_name":  "Power Imbalance Management",
            "question_text": "Does this paper explicitly mention the Management of power imbalance for the data collection, that is The differential exercise of control, authority, or influence during the research process .",
            "scoring_instructions": """"1 points - Not at all transparent No recognition or mention of power dynamics in the research No consideration of authority or influence issues; 2 points - Slightly transparent Brief acknowledgment that power differences might exist No description of how power issues were addressed; 3 points - Moderately transparent Recognizes some power imbalances in the research context Basic mention of strategies used to address power differences Limited discussion of power effects on the research; 4 points - Transparent Clear recognition of power dynamics and specific management strategies Explains how differential authority or influence was addressed Discusses how power management affected information gathering; 5 points - Fully transparent Comprehensive analysis of power dynamics throughout the research Detailed description of strategies used to manage different types of power imbalances Thorough discussion of how power management influenced the type and quality of information gathered """, 
            "output_field_name": "Power Imbalance Management Details"
        },
        {
            "id": 10,
            "criterion_short_name":  "Data Coding & First-Order Codes",
            "question_text": "In the data analysis phase, does this paper explicitly mention the Data coding and first-order codes , the process through which data are categorized to facilitate subsequent analysis (e.g., structural coding, descriptive coding, narrative coding) .",
            "scoring_instructions": """1 points - Not at all transparent No description of how data were analyzed or processed Cannot determine any coding or categorization procedures; 2 points - Slightly transparent Basic mention that data were "analyzed" or "coded" No details about coding procedures or approaches; 3 points - Moderately transparent Indicates coding approach (e.g., "thematic coding," "open coding") Some basic information about how data were categorized Limited procedural detail; 4 points - Transparent Clear description of coding procedures and approaches used Explains how data were categorized and codes developed Sufficient detail for understanding the coding process; 5 points - Fully transparent Comprehensive description of coding procedures and first-order code development Detailed explanation of categorization methods and quality assurance Thorough documentation that would allow other researchers to follow similar procedures """,
            "output_field_name": "First-Order Coding Process"
        },
        {
            "id": 11,
            "criterion_short_name":  "Data Analysis & Higher-Order Codes",
            "question_text": "In the data analysis phase, does this paper explicitly mention the Data analysis and second- and higher-order codes , The classification and interpretation of linguistic or visual material to make statements about implicit and explicit dimensions and structures and it is generally done by identifying key relationships that tie the first order codes together into a narrative or sequence . If there is no first order codes, there shouldn't be any second order codes.",
            "scoring_instructions": """1 points - Not at all transparent No description of analysis beyond mentioning that analysis was conducted Cannot determine how findings or themes were developed; 2 points - Slightly transparent Basic mention of "themes" or "patterns" without explanation No description of analytical procedures; 3 points - Moderately transparent Describes basic analytical approach (e.g., "thematic analysis") Some explanation of how themes or patterns were identified Limited detail about analytical procedures; 4 points - Transparent Clear description of analytical procedures and theme development Explains how codes were connected and interpreted Good explanation of how higher-order themes were developed; 5 points - Fully transparent Comprehensive explanation of the entire analytical process Detailed description of how codes were grouped and interpreted at multiple levels Thorough documentation of analytical procedures that would allow similar analytical approaches """,
            "output_field_name": "Higher-Order Coding Process"
        },
        {
            "id": 12,
            "criterion_short_name":  "Data Disclosure",
            "question_text": "Does this paper explicitly mention the Data disclosure, that is ensure that the materials needed to replicate research are available .",
            "scoring_instructions": """1 points - Not at all transparent No original data provided (no quotes, excerpts, or examples) Only presents researcher conclusions without supporting evidence; 2 points - Slightly transparent Very limited quotes or examples (minimal supporting material) Insufficient evidence to evaluate researcher claims; 3 points - Moderately transparent Some quotes or examples provided to support main findings Basic supporting evidence but limited in scope Some ability to verify key interpretations; 4 points - Transparent Good amount of original material supporting most claims Sufficient quotes and examples to understand key findings Enables readers to evaluate most interpretations; 5 points - Fully transparent Extensive original material throughout the findings Rich supporting evidence with detailed quotes and examples Sufficient data disclosure to allow readers to assess interpretation quality and potentially reach alternative conclusions """,
            "output_field_name": "Data Disclosure Statement"
        }
    ]

    return questions_data

def format_docs(docs):
    """Helper function to format retrieved documents for the prompt."""
    return "\n\n".join(doc.page_content for doc in docs)

class RITReviewerAssistant:
    def __init__(self, vector_store_path: str, openai_api_key: str, criteria_questions: list):
        self.vector_store_path = vector_store_path
        self.openai_api_key = openai_api_key
        self.criteria_questions = criteria_questions
        self.vector_store = None
        self.llm = ChatOpenAI(model_name=MODEL_NAME, temperature=0, openai_api_key=self.openai_api_key)
        self._load_vector_store()

    def _load_vector_store(self):
        """Loads an existing FAISS vector store."""
        if not pathlib.Path(self.vector_store_path).exists():
            logging.error(f"FAISS vector store not found at: {self.vector_store_path}")
            logging.error("Please ensure the vector store has been created and the path is correct.")
            raise FileNotFoundError(f"FAISS vector store not found at: {self.vector_store_path}")

        logging.info(f"Loading existing FAISS vector store from: {self.vector_store_path}")
        try:
            embeddings = OpenAIEmbeddings(openai_api_key=self.openai_api_key)
            self.vector_store = FAISS.load_local(
                self.vector_store_path,
                embeddings,
                allow_dangerous_deserialization=True # Required for FAISS with Langchain >= 0.1.14
            )
            logging.info("FAISS vector store loaded successfully.")
        except Exception as e:
            logging.error(f"Could not load existing vector store from {self.vector_store_path}: {e}", exc_info=True)
            raise

    def parse_review_output(self, review_text):
        """
        Parses the LLM output to extract Point, specified_details, and Where.
        Returns a dict with keys: point, specified_details, where.
        """
        # Use regex to extract the required fields
        point_match = re.search(r"Point:\s*(\d+)", review_text)
        details_match = re.search(r":\s*(.+)", review_text)
        where_match = re.search(r"Where:\s*(.+)", review_text)

        # Try to get the output_field_name from the review_text
        details_field = ""
        details_value = ""
        for line in review_text.splitlines():
            if line.startswith("Point:"):
                continue
            if line.startswith("Where:"):
                continue
            if ":" in line:
                details_field, details_value = line.split(":", 1)
                details_value = details_value.strip()
                break

        return {
            "point": point_match.group(1) if point_match else "",
            "specified_details": details_value,
            "where": where_match.group(1) if where_match else ""
        }

    def review_paper(self, csv_output_path=None):
        """Iterates through criteria questions and generates reviews using the loaded vector store.
        Optionally writes results to a CSV file.
        """
        if not self.vector_store:
            logging.error("Vector store not loaded. Cannot review paper.")
            return

        retriever = self.vector_store.as_retriever(search_kwargs={"k": 5})

        prompt_template = ChatPromptTemplate.from_messages([
            ("system", """You are a meticulous RIT (Research Transparency Index) reviewer. Your expertise is in evaluating research papers against specific transparency criteria.
You will be provided with context from a research paper and a specific criterion to evaluate.
Analyze the context thoroughly in relation to the criterion and its scoring instructions.

Your answer MUST strictly follow this format, with each item on a new line:
Point: <1, 2, 3, 4, or 5>
{output_field_name_placeholder}: <Specific details found that justify the score, e.g., "action research", "firm conditions and participant status described on page X", "saturation explicitly stated and justified in methods section">
Where: <Location in the paper, e.g., "Methodology section, paragraph 3", "Introduction, page 2, first paragraph", "Appendix A", "Not found">

Scoring Guidelines:
- 1 points: Not at all transparent: No procedures or choices are described.  For "Where:", state "Not found".
- 2 points: Slightly transparent: Some elements are described, but many key steps or decisions are missing or unclear. For "Where:", specify the location as precisely as possible from the context.
- 3 points: Moderately transparent: Core steps are outlined, but not all are explained in sufficient detail; rationales for choices may be partially addressed. For "Where:", specify the location as precisely as possible from the context.
- 4 points: Transparent: Most procedures are clearly described, including rationale for key decisions; some minor gaps or ambiguities remain. For "Where:", specify the location as precisely as possible from the context.
- 5 points: Fully transparent: All procedures and decisions are described thoroughly and coherently, with clear justification and reflexivity; readers can fully evaluate the trustworthiness of the study. For "Where:", specify the location as precisely as possible from the context.

    
Critically evaluate the provided context. Do not infer information that is not present.
If the context is insufficient to make a clear determination for for 2 or 3 or 4 or 5 points, err on the side of a lower score or state if the context doesn't cover the aspect.
Focus ONLY on the provided criterion."""),
            ("human", """Context from the research paper:
-------
{context}
-------

Criterion to evaluate:
{criterion_question}

Detailed Scoring Instructions for this criterion:
{criterion_scoring}

Based on the provided context and scoring instructions, please provide your review for this criterion.""")
        ])
        
        all_results = []
        csv_rows = []

        print(f"\n--- RIT Review ---")
        print(f"--- Vector Store: {self.vector_store_path} ---\n")

        for q_data in self.criteria_questions:
            logging.info(f"Reviewing Criterion ID {q_data['id']}: {q_data['criterion_short_name']}")
            
            rag_chain_for_question = (
                {
                    "context": lambda x: format_docs(retriever.invoke(x["criterion_question"])),
                    "criterion_question": lambda x: x["criterion_question"],
                    "criterion_scoring": lambda x: x["criterion_scoring"],
                    "output_field_name_placeholder": lambda x: x["output_field_name_placeholder"]
                }
                | prompt_template
                | self.llm
                | StrOutputParser()
            )

            try:
                result = rag_chain_for_question.invoke({
                    "criterion_question": q_data["question_text"],
                    "criterion_scoring": q_data["scoring_instructions"],
                    "output_field_name_placeholder": q_data["output_field_name"]
                })
                print(f"--- Criterion {q_data['id']}: {q_data['criterion_short_name']} ---")
                print(result)
                print("\n")
                all_results.append({"criterion_id": q_data['id'], "criterion_name": q_data['criterion_short_name'], "review": result})

                # Parse and add to CSV rows
                parsed = self.parse_review_output(result)
                csv_rows.append({
                    "question_id": q_data["id"],
                    "point": parsed["point"],
                    "specified_details": parsed["specified_details"],
                    "where": parsed["where"]
                })
            except Exception as e:
                logging.error(f"Error reviewing criterion ID {q_data['id']}: {e}", exc_info=True)
                print(f"--- Criterion {q_data['id']}: {q_data['criterion_short_name']} ---")
                print("Error: Could not generate review for this criterion.")
                print("\n")
                all_results.append({"criterion_id": q_data['id'], "criterion_name": q_data['criterion_short_name'], "review": "Error during review."})
                csv_rows.append({
                    "question_id": q_data["id"],
                    "point": "",
                    "specified_details": "",
                    "where": ""
                })


        if csv_output_path:
            with open(csv_output_path, "w", newline='', encoding="utf-8") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=["question_id", "point", "specified_details", "where"])
                writer.writeheader()
                writer.writerows(csv_rows)
            print(f"\nCSV output saved to: {csv_output_path}")

        return all_results

if __name__ == "__main__":
    try:
        openai_api_key = load_api_key()
        current_dir = pathlib.Path(__file__).parent.resolve()


        pdf_name = "14-ASQ-Michel"
        vector_store_folder_name = "docling_script_output/faiss_index_" + pdf_name   
        vector_store_path = str(current_dir / vector_store_folder_name)


        csv_output_path = str(current_dir / "rit_review_output" / ("rit_review_" + pdf_name + "_specified.csv"))

        if not pathlib.Path(vector_store_path).exists():
            logging.error(f"Vector store not found at: {vector_store_path}")
            print(f"ERROR: Pre-existing FAISS vector store not found at '{vector_store_path}'.")
            print("Please ensure the path is correct and the vector store was created by a previous script.")
        else:
            questions = load_criteria_questions()
            if not questions:
                logging.error("No questions loaded. Exiting.")
            else:
                assistant = RITReviewerAssistant(
                    vector_store_path=vector_store_path,
                    openai_api_key=openai_api_key,
                    criteria_questions=questions
                )
                assistant.review_paper(csv_output_path=csv_output_path)

    except ValueError as ve:
        print(f"Configuration Error: {ve}")
    except FileNotFoundError as fnfe:
        print(f"File Not Found Error: {fnfe}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in main: {e}", exc_info=True)
        print(f"An unexpected error occurred: {e}")