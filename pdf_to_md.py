import pathlib
import logging
import json



try:
    from docling.document_converter import Document<PERSON>onverter, FormatOption
    from docling.datamodel.pipeline_options import PdfPipelineOptions, TableFormerMode, EasyOcrOptions
    from docling.datamodel.base_models import InputFormat, ConversionStatus

    from docling.backend.docling_parse_v2_backend import DoclingParseV2DocumentBackend
    from docling.pipeline.standard_pdf_pipeline import StandardPdfPipeline
    from tabulate import tabulate
    

    DOCLING_AND_TABULATE_AVAILABLE = True
    logging.info("Successfully imported Docling components and tabulate.")
except ImportError as e:
    logging.error(
        f"Failed to import Docling components or tabulate: {e}. "
        "This script requires Docling and tabulate to be installed and configured. "
        "Please ensure they are correctly set up in your Python environment. "
        "The script will not be able to parse PDFs without them."
    )
    DOCLING_AND_TABULATE_AVAILABLE = False

# --- Setup basic logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)
logging.info("--- LOGGER INITIALIZED --- This message confirms basicConfig has run and force=True was used (requires Python 3.8+).")

class StandalonePdfToMarkdownConverter:
    def __init__(self, output_base_dir: pathlib.Path):
        self.output_base_dir = output_base_dir
        self.intermediate_data_dir = self.output_base_dir / "01_intermediate_docling_data"
        self.markdown_output_dir = self.output_base_dir / "02_markdown_output"

        self.intermediate_data_dir.mkdir(parents=True, exist_ok=True)
        self.markdown_output_dir.mkdir(parents=True, exist_ok=True)
        logging.info(f"Output directories initialized under: {self.output_base_dir.resolve()}")

        self.doc_converter = None
        DOCLING_AND_TABULATE_AVAILABLE = True
        if DOCLING_AND_TABULATE_AVAILABLE:
            try:
                # Initialize Docling's DocumentConverter
                # This configuration is based on PDFParser._create_document_converter
                # from your provided src/pdf_parsing.py
                pipeline_options = PdfPipelineOptions()
   
                # pipeline_options.do_ocr = True # Default from your code
                # pipeline_options.ocr_options = EasyOcrOptions(lang=['en'], force_full_page_ocr=False) # Default
                # pipeline_options.do_table_structure = True # Default
                # pipeline_options.table_structure_options.do_cell_matching = True # Default
                # pipeline_options.table_structure_options.mode = TableFormerMode.ACCURATE # Default

                # Using DoclingParseV2DocumentBackend as seen in your project
                pdf_backend = DoclingParseV2DocumentBackend

                format_options = {
                    InputFormat.PDF: FormatOption(
                        pipeline_cls=StandardPdfPipeline,
                        pipeline_options=pipeline_options,
                        backend=pdf_backend
                    )
                }
                self.doc_converter = DocumentConverter(format_options=format_options)
                logging.info("Docling DocumentConverter initialized successfully.")
            except Exception as e:
                logging.error(f"Critical error initializing Docling DocumentConverter: {e}", exc_info=True)
                # If initialization fails, we can't proceed with Docling.
                DOCLING_AND_TABULATE_AVAILABLE = False # Mark as unavailable to prevent further errors
        else:
            logging.warning("Docling or tabulate not available; PDF parsing functionality will be missing.")


    def _parse_pdf_with_docling(self, pdf_path: pathlib.Path) -> tuple[dict | None, list | None]:
        """
        Parses a single PDF file using the initialized Docling DocumentConverter.

        Returns:
            A tuple: (raw_docling_dict, list_of_docling_table_objects).
            Returns (None, None) on failure or if Docling is not available.
        """
        if not DOCLING_AND_TABULATE_AVAILABLE or not self.doc_converter:
            logging.error("Docling converter not available or not initialized. Cannot parse PDF.")
            return None, None
        print(f"Parsing PDF with Docling: {pdf_path.name}")
        logging.info(f"Parsing PDF with Docling: {pdf_path.name}")
        try:
            conversion_results = self.doc_converter.convert_all(source=[pdf_path])
            try:
                conv_res = next(conversion_results)
            except StopIteration:
                logging.warning(f"Docling conversion returned no results (empty generator) for {pdf_path.name}.")
                return None, None
            if conv_res.status != ConversionStatus.SUCCESS:
                logging.error(f"Docling conversion failed for {pdf_path.name}. Status: {conv_res.status}")
                return None, None

            if conv_res.document is None:
                logging.error(f"Docling conversion succeeded but document is None for {pdf_path.name}")
                return None, None
                
            docling_dict = conv_res.document.export_to_dict()
            docling_tables_list = conv_res.document.tables # List of Docling table objects
            return docling_dict, docling_tables_list

        except Exception as e:
            logging.error(f"Exception during Docling parsing of {pdf_path.name}: {e}", exc_info=True)
            return None, None

    def _transform_docling_output_to_internal_structure(self, raw_docling_data: dict, docling_tables_list: list, pdf_filename: str) -> dict:
        """
        Transforms raw Docling dictionary output and table objects into this script's
        internal standardized structure. This is inspired by your JsonReportProcessor.
        """
        logging.info(f"Transforming Docling output for {pdf_filename} to internal structure.")
        internal_pages_map = {} # page_num -> {"page_number": X, "elements": []}

        if not raw_docling_data:
            logging.warning(f"No raw Docling data to transform for {pdf_filename}.")
            return {"pdf_filename": pdf_filename, "total_pages": 0, "pages": []}

        all_docling_texts = raw_docling_data.get('texts', [])
        all_docling_table_metadata = raw_docling_data.get('tables', []) # Metadata like 'prov'
        all_docling_pictures = raw_docling_data.get('pictures', [])

        # Process elements as referenced in the document body structure
        # This mirrors the approach in your JsonReportProcessor.assemble_content
        for item_ref_data in raw_docling_data.get('body', {}).get('children', []):
            if not isinstance(item_ref_data, dict) or '$ref' not in item_ref_data:
                logging.warning(f"Skipping invalid item reference in body: {item_ref_data}")
                continue
            
            ref_path = item_ref_data['$ref']
            try:
                # Assuming format like '#/texts/0' or '#/tables/0'
                _, ref_type, ref_idx_str = ref_path.split('/')
                ref_idx = int(ref_idx_str)
            except ValueError:
                logging.warning(f"Could not parse $ref: {ref_path} in {pdf_filename}")
                continue

            page_num = None
            element_data = None
            element_prov = None

            if ref_type == 'texts' and 0 <= ref_idx < len(all_docling_texts):
                docling_text_item = all_docling_texts[ref_idx]
                element_prov = docling_text_item.get('prov', [{}])[0] # Get first provenance item
                page_num = element_prov.get('page_no')
                text_content = docling_text_item.get('text', '').strip()
                label = docling_text_item.get('label', 'text') # e.g., paragraph, section_header

                if text_content: # Only add if there's actual text
                    if label in ['section_header', 'title', 'header']: # Common labels for headers
                        # Determine level based on label or other heuristics if available
                        level = 1 if label == 'title' else 2 if label == 'section_header' else 3
                        element_data = {"type": "header", "level": level, "text": text_content}
                    elif label == 'list_item':
                        # Docling might provide marker info, or we deduce it.
                        element_data = {"type": "list_item", "text": text_content, "marker": "-"} # Default marker
                    elif label == 'footnote':
                        element_data = {"type": "footnote", "text": text_content}
                    else: # Default to paragraph
                        element_data = {"type": "paragraph", "text": text_content}
            
            elif ref_type == 'tables' and 0 <= ref_idx < len(all_docling_table_metadata):
                docling_table_meta = all_docling_table_metadata[ref_idx]
                docling_table_obj = docling_tables_list[ref_idx] if docling_tables_list and 0 <= ref_idx < len(docling_tables_list) else None
                
                element_prov = docling_table_meta.get('prov', [{}])[0]
                page_num = element_prov.get('page_no')

                if docling_table_obj:
                    try:
                        # Use model_dump to get table structure, similar to your JsonReportProcessor
                        table_structure = docling_table_obj.model_dump()
                        grid_data = []
                        # Path to grid: table_structure['data']['grid']
                        for row_cells in table_structure.get('data', {}).get('grid', []):
                            grid_data.append([cell.get('text', '') for cell in row_cells])
                        
                        md_table_str = ""
                        if grid_data:
                            # Simple header assumption: first row of grid_data is header
                            # Your original code has more robust header handling in tabulate call
                            headers = grid_data[0] if grid_data else "firstrow"
                            data_rows = grid_data[1:] if len(grid_data) > 1 and headers != "firstrow" else grid_data if headers == "firstrow" else grid_data

                            md_table_str = tabulate(data_rows, headers=headers, tablefmt="github")
                        element_data = {"type": "markdown_table", "markdown_text": md_table_str}
                    except Exception as e_tab:
                        logging.warning(f"Could not process table {ref_idx} into Markdown for {pdf_filename}: {e_tab}")
                        element_data = {"type": "paragraph", "text": f"[Table {ref_idx} - Content Error]"}
                else:
                     element_data = {"type": "paragraph", "text": f"[Table {ref_idx} - Object Missing]"}


            elif ref_type == 'pictures' and 0 <= ref_idx < len(all_docling_pictures):
                docling_pic_item = all_docling_pictures[ref_idx]
                element_prov = docling_pic_item.get('prov', [{}])[0]
                page_num = element_prov.get('page_no')
                # For simplicity, just note the picture. Real handling is complex.
                element_data = {"type": "paragraph", "text": f"[Image Reference {ref_idx} on page {page_num}]"}

            if page_num is not None and element_data:
                if page_num not in internal_pages_map:
                    internal_pages_map[page_num] = {"page_number": page_num, "elements": []}
                internal_pages_map[page_num]["elements"].append(element_data)
        
        sorted_pages = [internal_pages_map[pn] for pn in sorted(internal_pages_map.keys())]
        return {
            "pdf_filename": pdf_filename,
            "total_pages": len(sorted_pages),
            "pages": sorted_pages,
        }

    def _process_internal_structured_data(self, internal_data: dict) -> dict:
        """Performs final cleaning/adjustments on the script's internal standardized structure."""
        pdf_filename = internal_data.get("pdf_filename", "Unknown Document")
        logging.info(f"Performing final processing on internal structure for: {pdf_filename}")
        
        for page in internal_data.get("pages", []):
            final_elements = []
            temp_paragraph_texts = [] # To merge consecutive paragraph texts
            for element in page.get("elements", []):
                # Consolidate consecutive paragraph texts
                if element.get("type") == "paragraph":
                    text = element.get("text", "").strip()
                    if text:
                        temp_paragraph_texts.append(text)
                else: # Non-paragraph element
                    if temp_paragraph_texts: # Finalize any pending paragraph
                        final_elements.append({"type": "paragraph", "text": " ".join(temp_paragraph_texts)})
                        temp_paragraph_texts = []
                    
                    # Add other elements if they have content
                    if element.get("type") == "markdown_table" and element.get("markdown_text"):
                        final_elements.append(element)
                    elif element.get("text","").strip(): # For headers, list_items, footnotes
                         final_elements.append(element)
            
            if temp_paragraph_texts: # Add any trailing paragraph
                final_elements.append({"type": "paragraph", "text": " ".join(temp_paragraph_texts)})
            
            page["elements"] = [el for el in final_elements if el] # Remove any truly empty elements if any slipped
            
        return internal_data

    def _generate_markdown_from_processed_data(self, processed_data: dict) -> str:
        """Generates Markdown string from the processed internal data structure."""
        pdf_filename = processed_data.get("pdf_filename", "Untitled Document")
        logging.info(f"Generating Markdown for: {pdf_filename}")
        markdown_parts = [f"# Document: {pdf_filename}\n"]

        for page in processed_data.get("pages", []):
            markdown_parts.append(f"\n## Page {page.get('page_number', 'N/A')}\n")
            if not page.get("elements"):
                markdown_parts.append("*(This page appears to be empty or contained non-textual elements not converted)*\n")

            for element in page.get("elements", []):
                el_type = element.get("type")
                text_content = element.get("text", "")
                markdown_text = element.get("markdown_text", "") # For pre-formatted markdown

                if el_type == "header":
                    level = element.get("level", 1)
                    if text_content: markdown_parts.append(f"{'#' * level} {text_content}\n")
                elif el_type == "paragraph":
                    if text_content: markdown_parts.append(f"{text_content}\n\n") # Add extra newline for paragraph spacing
                elif el_type == "list_item":
                    marker = element.get("marker", "-").strip()
                    if text_content: markdown_parts.append(f"{marker} {text_content}\n")
                elif el_type == "footnote":
                     if text_content: markdown_parts.append(f"[^footnote]: {text_content}\n") # Basic footnote style
                elif el_type == "markdown_table":
                    if markdown_text:
                        markdown_parts.append(markdown_text + "\n\n")
                else: # Fallback for any other text
                    if text_content: markdown_parts.append(f"{text_content}\n\n")
            markdown_parts.append("\n---\n") # Page separator

        return "".join(markdown_parts)

    def convert_directory_to_markdown(self, pdf_input_dir: pathlib.Path):
        logging.info(f"--- Starting PDF to Markdown Conversion (Docling based) for: {pdf_input_dir.resolve()} ---")

        if not DOCLING_AND_TABULATE_AVAILABLE:
            logging.critical("Docling and/or tabulate are not available. Cannot proceed with PDF conversion.")
            return

        if not pdf_input_dir.is_dir():
            logging.error(f"Input directory not found: {pdf_input_dir}. Aborting.")
            return

        pdf_files = list(pdf_input_dir.glob("*.pdf"))
        if not pdf_files:
            logging.warning(f"No PDF files found in {pdf_input_dir}.")
            return

        for pdf_file_path in pdf_files:
            logging.info(f"Processing file: {pdf_file_path.name}")
            raw_docling_data, docling_tables_list = self._parse_pdf_with_docling(pdf_file_path)
            if raw_docling_data is None:
                logging.error(f"Skipping {pdf_file_path.name} due to parsing error or Docling failure.")
                continue
            
            intermediate_json_path = self.intermediate_data_dir / f"{pdf_file_path.stem}_docling_raw.json"
            try:
                with open(intermediate_json_path, "w", encoding="utf-8") as f_json:
                    # For table objects, we can't directly serialize them unless they are Pydantic models or similar.
                    # We'll serialize the raw_docling_data; the tables_list is used in transformation.
                    json.dump(raw_docling_data, f_json, indent=2, ensure_ascii=False)
                logging.info(f"Saved raw Docling dict output for {pdf_file_path.name} to {intermediate_json_path.name}")
            except Exception as e:
                logging.error(f"Could not save raw Docling dict output for {pdf_file_path.name}: {e}")

            internal_structured_data = self._transform_docling_output_to_internal_structure(
                raw_docling_data, docling_tables_list, pdf_file_path.name
            )
            final_processed_data = self._process_internal_structured_data(internal_structured_data)
            markdown_content = self._generate_markdown_from_processed_data(final_processed_data)
            
            markdown_filename = pdf_file_path.stem + ".md"
            markdown_filepath = self.markdown_output_dir / markdown_filename
            try:
                with open(markdown_filepath, "w", encoding="utf-8") as f_md:
                    f_md.write(markdown_content)
                logging.info(f"Exported Markdown for {pdf_file_path.name} to {markdown_filepath.name}")
            except IOError as e:
                logging.error(f"Failed to write Markdown for {pdf_file_path.name}: {e}")

        logging.info(f"--- PDF to Markdown Conversion (Docling based) Finished for: {pdf_input_dir.resolve()} ---")

# --- Main execution block for demonstration ---
if __name__ == "__main__":
    current_script_dir = pathlib.Path(__file__).parent.resolve()
    # Create a dummy input directory for PDF files
    dummy_pdf_input_dir = current_script_dir / "sample_pdfs_for_docling_script"
    dummy_pdf_input_dir.mkdir(parents=True, exist_ok=True)


    # Define the main output directory
    main_output_dir = current_script_dir / "docling_script_output"

    logging.info("Initializing StandalonePdfToMarkdownConverter (for Docling)...")
    if not DOCLING_AND_TABULATE_AVAILABLE:
        print("\nWARNING: Docling or Tabulate is not available. The script will run with conceptual Docling logic if placeholders are active, or fail if not.")
        print("For actual PDF processing, please ensure Docling and Tabulate are installed and configured correctly.\n")
        # If using placeholder classes, the script might still "run" but won't process real PDFs.
        # If no placeholders and imports failed, the converter init might fail or methods won't work.

    converter = StandalonePdfToMarkdownConverter(output_base_dir=main_output_dir)

    DOCLING_AND_TABULATE_AVAILABLE = True
    
    if DOCLING_AND_TABULATE_AVAILABLE or hasattr(converter, 'doc_converter') and converter.doc_converter is not None : # Check if converter was initialized
        logging.info("Starting the conversion process...")
        converter.convert_directory_to_markdown(pdf_input_dir=dummy_pdf_input_dir)
    else:
        logging.error("Cannot start conversion process as Docling components are not properly initialized.")


    print(f"\nScript execution finished.")
    print(f"Please check the '{main_output_dir.name}' directory (relative to this script) for output files.")