import os
import pathlib
import logging
import pandas as pd
import csv
import re
from dotenv import load_dotenv

# Langchain imports
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.vectorstores import FAISS
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser

# --- Setup basic logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)
logging.info("--- LOGGER INITIALIZED --- This message confirms basicConfig has run and force=True was used (requires Python 3.8+).")


# --- Configuration ---
MODEL_NAME = "gpt-4o-mini" # 

def load_api_key():
    """Loads OpenAI API key from .env file or environment variables."""
    load_dotenv()
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY not found. Please set it in environment variables or a .env file.")
        raise ValueError("OPENAI_API_KEY not found.")
    return api_key

def load_criteria_questions() -> list:
    """
    Loads criteria questions and details from the provided CSV file and the prompt.
    The CSV is primarily used for the 'criterion_short_name'.
    """

    # The 12 questions and their detailed scoring instructions (as provided by the user)
    # output_field_name is used to customize the prompt for the specific detail line.
    questions_data = [
        {
            "id": 1,
            "criterion_short_name":  "Qualitative Methodology",
            "question_text": "Does this paper explicitly described the particular qualitative methodology used in the study (e.g., action research, case study, grounded theory)?",
            "scoring_instructions": "0 points for No information regarding qualitative methodology used found, 2 points when qualitative methodology was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when qualitative methodology was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Research Strategy"
        },
        {
            "id": 2,
            "criterion_short_name":  "Study Milieu",
            "question_text": "Does this paper explicitly described the physical, social, and cultural milieu of the study (e.g., firm conditions, industry, participants' social status)?",
            "scoring_instructions": "0 points for No information regarding the physical, social, and cultural milieu of the study can be found, 2 points when the physical, social, and cultural milieu of the study was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when the physical, social, and cultural milieu of the study was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Study Milieu Context"
        },
        {
            "id": 3,
            "criterion_short_name":  "Researcher Relationship",
            "question_text": "Does this paper explicitly described The researcher's relationship with the organization and study participants; the closer the relationship, the more the researcher is an insider rather than an outsider?",
            "scoring_instructions": "0 points for No information regarding the researcher's relationship with the organization and study participants of the study can be found, 2 points when the researcher's relationship with the organization and study participants of the study was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when the researcher's relationship with the organization and study participants of the study was both mentioned and explained in details (if 4 points, please tell where it happened). If only the relational exchanges among participants mentioned, we don't count them as scored.",
            "output_field_name": "Researcher Relationship Details"
        },
        {
            "id": 4,
            "criterion_short_name":  "Participant Selection",
            "question_text": "Does this paper explicitly described how participants , interviewees or cases are selected for the study. .",
            "scoring_instructions": "0 points for No information regarding to how participants , interviewees or cases are selected for the study can be found, 2 points when how participants , interviewees or cases are selected for the studywas briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when how participants , interviewees or cases are selected for the study was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Participant Selection Method"
        },
        {
            "id": 5,
            "criterion_short_name":  "Participant Importance",
            "question_text": "Does this paper explicitly described Relative importance of the participants/cases for the study, that is The study's sample and the relative importance of each participant or case .",
            "scoring_instructions": "0 points for No information regarding to Relative importance of the participants/cases for the study can be found, 2 points when Relative importance of the participants/cases for the study was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when Relative importance of the participants/cases for the study was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Participant Importance Details"
        },
        {
            "id": 6,
            "criterion_short_name":  "Interaction Documentation",
            "question_text": "Does this paper explicitly Documenting interactions with participants for the study, The documentation and transcription of the interviews and all other forms of observations (e.g., audio, video, notations) .",
            "scoring_instructions": "0 points for No information regarding to Documenting interactions with participants for the study can be found, 2 points when Documenting interactions with participants for the study was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when Documenting interactions with participants for the study was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Interaction Documentation Method"
        },
        {
            "id": 7,
            "criterion_short_name":  "Saturation Point",
            "question_text": "Does this paper explicitly Saturation point a Saturation point for the data collection, which occurs when there are no new insights or themes in the process of collecting data and drawing conclusions .",
            "scoring_instructions": "0 points for No information regarding to Saturation point for the study can be found, 2 points when Saturation point for the study was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when Saturation pointfor the study was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Saturation Point Details"
        },
        {
            "id": 8,
            "criterion_short_name":  "Unexpected Events",
            "question_text": "Does this paper explicitly mentioned Unexpected opportunities, challenges, and other events. Unexpected opportunities (e.g., access to additional sources of data), challenges (e.g., a firm's unit declines to participate in the last data collection stage and is replaced by a different one), and events (e.g., internal and external changes such as a new CEO or changes in market conditions during the study) that occur during all stages of the research process .",
            "scoring_instructions": "0 points for No information regarding to Unexpected opportunities, challenges, and other events. for the study can be found, 2 points when Unexpected opportunities, challenges, and other events. for the study was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when Unexpected opportunities, challenges, and other events for the study was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Unexpected Events Management"
        },
        {
            "id": 9,
            "criterion_short_name":  "Power Imbalance Management",
            "question_text": "Does this paper explicitly mention the Management of power imbalance for the data collection, that is The differential exercise of control, authority, or influence during the research process .",
            "scoring_instructions": "0 points for No information regarding to the Management of power imbalance for the data collection can be found, 2 points when the Management of power imbalance for the data collection was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when the Management of power imbalance for the data collection was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Power Imbalance Management Details"
        },
        {
            "id": 10,
            "criterion_short_name":  "Data Coding & First-Order Codes",
            "question_text": "In the data analysis phase, does this paper explicitly mention the Data coding and first-order codes , the process through which data are categorized to facilitate subsequent analysis (e.g., structural coding, descriptive coding, narrative coding) .",
            "scoring_instructions": "0 points for No information regarding to the Data coding and first-order codes can be found, 2 points when the Data coding and first-order codes was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when the Data coding and first-order codes was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "First-Order Coding Process"
        },
        {
            "id": 11,
            "criterion_short_name":  "Data Analysis & Higher-Order Codes",
            "question_text": "In the data analysis phase, does this paper explicitly mention the Data analysis and second- and higher-order codes , The classification and interpretation of linguistic or visual material to make statements about implicit and explicit dimensions and structures and it is generally done by identifying key relationships that tie the first order codes together into a narrative or sequence . If there is no first order codes, there shouldn't be any second order codes.",
            "scoring_instructions": "0 points for No information regarding to the the Data analysis and second- and higher-order codes can be found, 2 points when the the Data analysis and second- and higher-order codes was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when the the Data analysis and second- and higher-order codes was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Higher-Order Coding Process"
        },
        {
            "id": 12,
            "criterion_short_name":  "Data Disclosure",
            "question_text": "Does this paper explicitly mention the Data disclosure, that is ensure that the materials needed to replicate research are available .",
            "scoring_instructions": "0 points for No information regarding to the Data disclosure can be found, 2 points when the Data disclosure was briefly mentioned, with no explanation (if 2 points, please tell where it happened), 4 points when the Data disclosure was both mentioned and explained in details (if 4 points, please tell where it happened).",
            "output_field_name": "Data Disclosure Statement"
        }
    ]

    return questions_data

def format_docs(docs):
    """Helper function to format retrieved documents for the prompt."""
    return "\n\n".join(doc.page_content for doc in docs)

class RITReviewerAssistant:
    def __init__(self, vector_store_path: str, openai_api_key: str, criteria_questions: list):
        self.vector_store_path = vector_store_path
        self.openai_api_key = openai_api_key
        self.criteria_questions = criteria_questions
        self.vector_store = None
        self.llm = ChatOpenAI(model_name=MODEL_NAME, temperature=0, openai_api_key=self.openai_api_key)
        self._load_vector_store()

    def _load_vector_store(self):
        """Loads an existing FAISS vector store."""
        if not pathlib.Path(self.vector_store_path).exists():
            logging.error(f"FAISS vector store not found at: {self.vector_store_path}")
            logging.error("Please ensure the vector store has been created and the path is correct.")
            raise FileNotFoundError(f"FAISS vector store not found at: {self.vector_store_path}")

        logging.info(f"Loading existing FAISS vector store from: {self.vector_store_path}")
        try:
            embeddings = OpenAIEmbeddings(openai_api_key=self.openai_api_key)
            self.vector_store = FAISS.load_local(
                self.vector_store_path,
                embeddings,
                allow_dangerous_deserialization=True # Required for FAISS with Langchain >= 0.1.14
            )
            logging.info("FAISS vector store loaded successfully.")
        except Exception as e:
            logging.error(f"Could not load existing vector store from {self.vector_store_path}: {e}", exc_info=True)
            raise

    def parse_review_output(self, review_text):
        """
        Parses the LLM output to extract Point, specified_details, and Where.
        Returns a dict with keys: point, specified_details, where.
        """
        # Use regex to extract the required fields
        point_match = re.search(r"Point:\s*(\d+)", review_text)
        details_match = re.search(r":\s*(.+)", review_text)
        where_match = re.search(r"Where:\s*(.+)", review_text)

        # Try to get the output_field_name from the review_text
        details_field = ""
        details_value = ""
        for line in review_text.splitlines():
            if line.startswith("Point:"):
                continue
            if line.startswith("Where:"):
                continue
            if ":" in line:
                details_field, details_value = line.split(":", 1)
                details_value = details_value.strip()
                break

        return {
            "point": point_match.group(1) if point_match else "",
            "specified_details": details_value,
            "where": where_match.group(1) if where_match else ""
        }

    def review_paper(self, csv_output_path=None):
        """Iterates through criteria questions and generates reviews using the loaded vector store.
        Optionally writes results to a CSV file.
        """
        if not self.vector_store:
            logging.error("Vector store not loaded. Cannot review paper.")
            return

        retriever = self.vector_store.as_retriever(search_kwargs={"k": 5})

        prompt_template = ChatPromptTemplate.from_messages([
            ("system", """You are a meticulous RIT (Research Transparency Index) reviewer. Your expertise is in evaluating research papers against specific transparency criteria.
You will be provided with context from a research paper and a specific criterion to evaluate.
Analyze the context thoroughly in relation to the criterion and its scoring instructions.

Your answer MUST strictly follow this format, with each item on a new line:
Point: <0, 2, or 4>
{output_field_name_placeholder}: <Specific details found that justify the score, e.g., "action research", "firm conditions and participant status described on page X", "saturation explicitly stated and justified in methods section">
Where: <Location in the paper, e.g., "Methodology section, paragraph 3", "Introduction, page 2, first paragraph", "Appendix A", "Not found">

Scoring Guidelines:
- 0 points: If the information related to the criterion is not found in the provided context. For "Where:", state "Not found".
- 2 points: If the information is briefly mentioned with no or minimal explanation. For "Where:", specify the location as precisely as possible from the context.
- 4 points: If the information is both mentioned AND explained in detail. For "Where:", specify the location as precisely as possible.

Critically evaluate the provided context. Do not infer information that is not present.
If the context is insufficient to make a clear determination for 2 or 4 points, err on the side of a lower score or state if the context doesn't cover the aspect.
Focus ONLY on the provided criterion."""),
            ("human", """Context from the research paper:
-------
{context}
-------

Criterion to evaluate:
{criterion_question}

Detailed Scoring Instructions for this criterion:
{criterion_scoring}

Based on the provided context and scoring instructions, please provide your review for this criterion.""")
        ])
        
        all_results = []
        csv_rows = []

        print(f"\n--- RIT Review ---")
        print(f"--- Vector Store: {self.vector_store_path} ---\n")

        for q_data in self.criteria_questions:
            logging.info(f"Reviewing Criterion ID {q_data['id']}: {q_data['criterion_short_name']}")
            
            rag_chain_for_question = (
                {
                    "context": lambda x: format_docs(retriever.invoke(x["criterion_question"])),
                    "criterion_question": lambda x: x["criterion_question"],
                    "criterion_scoring": lambda x: x["criterion_scoring"],
                    "output_field_name_placeholder": lambda x: x["output_field_name_placeholder"]
                }
                | prompt_template
                | self.llm
                | StrOutputParser()
            )

            try:
                result = rag_chain_for_question.invoke({
                    "criterion_question": q_data["question_text"],
                    "criterion_scoring": q_data["scoring_instructions"],
                    "output_field_name_placeholder": q_data["output_field_name"]
                })
                print(f"--- Criterion {q_data['id']}: {q_data['criterion_short_name']} ---")
                print(result)
                print("\n")
                all_results.append({"criterion_id": q_data['id'], "criterion_name": q_data['criterion_short_name'], "review": result})

                # Parse and add to CSV rows
                parsed = self.parse_review_output(result)
                csv_rows.append({
                    "question_id": q_data["id"],
                    "point": parsed["point"],
                    "specified_details": parsed["specified_details"],
                    "where": parsed["where"]
                })
            except Exception as e:
                logging.error(f"Error reviewing criterion ID {q_data['id']}: {e}", exc_info=True)
                print(f"--- Criterion {q_data['id']}: {q_data['criterion_short_name']} ---")
                print("Error: Could not generate review for this criterion.")
                print("\n")
                all_results.append({"criterion_id": q_data['id'], "criterion_name": q_data['criterion_short_name'], "review": "Error during review."})
                csv_rows.append({
                    "question_id": q_data["id"],
                    "point": "",
                    "specified_details": "",
                    "where": ""
                })


        if csv_output_path:
            with open(csv_output_path, "w", newline='', encoding="utf-8") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=["question_id", "point", "specified_details", "where"])
                writer.writeheader()
                writer.writerows(csv_rows)
            print(f"\nCSV output saved to: {csv_output_path}")

        return all_results

if __name__ == "__main__":
    try:
        openai_api_key = load_api_key()
        current_dir = pathlib.Path(__file__).parent.resolve()


        vector_store_folder_name = "docling_script_output/faiss_index_2"  
        vector_store_path = str(current_dir / vector_store_folder_name)


        csv_output_path = str(current_dir / "rit_review_output_2.csv")

        if not pathlib.Path(vector_store_path).exists():
            logging.error(f"Vector store not found at: {vector_store_path}")
            print(f"ERROR: Pre-existing FAISS vector store not found at '{vector_store_path}'.")
            print("Please ensure the path is correct and the vector store was created by a previous script.")
        else:
            questions = load_criteria_questions()
            if not questions:
                logging.error("No questions loaded. Exiting.")
            else:
                assistant = RITReviewerAssistant(
                    vector_store_path=vector_store_path,
                    openai_api_key=openai_api_key,
                    criteria_questions=questions
                )
                assistant.review_paper(csv_output_path=csv_output_path)

    except ValueError as ve:
        print(f"Configuration Error: {ve}")
    except FileNotFoundError as fnfe:
        print(f"File Not Found Error: {fnfe}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in main: {e}", exc_info=True)
        print(f"An unexpected error occurred: {e}")