import os
import pathlib
import logging
from dotenv import load_dotenv, dotenv_values

# Langchain imports
from langchain_community.document_loaders import TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_api_key():
    """Loads OpenAI API key from a local .env file only."""
    env_path = ".env"
    env_vars = dotenv_values(env_path)
    api_key = env_vars.get("OPENAI_API_KEY")
    if not api_key:
        logging.error("OPENAI_API_KEY not found in local .env file.")
        print("Please set your OPENAI_API_KEY in a local .env file.")
        print("Example .env file content: OPENAI_API_KEY='your_actual_api_key_here'")
        return None
    return api_key

def create_embeddings_and_vector_store(markdown_file_path: str, vector_store_path: str, openai_api_key: str):
    """
    Loads a Markdown file, splits its text, creates OpenAI embeddings,
    and saves them to a FAISS vector store.

    Args:
        markdown_file_path (str): Path to the input Markdown file.
        vector_store_path (str): Path where the FAISS vector store will be saved.
        openai_api_key (str): The OpenAI API key.
    """
    try:
        # 1. Load the Markdown document
        logging.info(f"Loading Markdown file: {markdown_file_path}")
        loader = TextLoader(markdown_file_path, encoding="utf-8")
        documents = loader.load()
        if not documents:
            logging.error(f"No documents were loaded from {markdown_file_path}. Check the file content and path.")
            return
        logging.info(f"Successfully loaded {len(documents)} document(s). Total characters: {sum(len(doc.page_content) for doc in documents)}")

        # 2. Split the document into chunks
        # You can adjust chunk_size and chunk_overlap as needed.
        # For Markdown, RecursiveCharacterTextSplitter is generally good.
        # You could also explore MarkdownHeaderTextSplitter if your MD has a very consistent header structure.
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,  # Max characters per chunk
            chunk_overlap=200, # Characters to overlap between chunks
            length_function=len,
            is_separator_regex=False,
            separators=["\n\n", "\n", " ", "", "## ", "# "] # Common Markdown separators
        )
        split_documents = text_splitter.split_documents(documents)
        if not split_documents:
            logging.error("Text splitting resulted in no chunks. Check the document content or splitter settings.")
            return
        logging.info(f"Document split into {len(split_documents)} chunks.")
        for i, chunk in enumerate(split_documents[:2]): # Log first 2 chunks for verification
             logging.info(f"Chunk {i} sample: {chunk.page_content[:200]}...")


        # 3. Initialize OpenAI embeddings model
        logging.info("Initializing OpenAI embeddings model...")
        try:
            embeddings = OpenAIEmbeddings(openai_api_key=openai_api_key)
            # Test embedding a small piece of text (optional, good for debugging API key issues)
            # _ = embeddings.embed_query("Test query")
            # logging.info("OpenAI embeddings model initialized and tested successfully.")
        except Exception as e:
            logging.error(f"Failed to initialize OpenAIEmbeddings: {e}")
            logging.error("Please ensure your OpenAI API key is valid and has embedding permissions.")
            return

        # 4. Create FAISS vector store from documents and embeddings
        logging.info("Creating FAISS vector store from document chunks and embeddings...")
        vector_store = FAISS.from_documents(documents=split_documents, embedding=embeddings)
        logging.info("FAISS vector store created successfully.")

        # 5. Save the FAISS vector store locally
        vector_store.save_local(vector_store_path)
        logging.info(f"FAISS vector store saved locally to: {vector_store_path}")
        print(f"\nSuccessfully created and saved the vector store at: {vector_store_path}")
        print(f"You can now load it using: FAISS.load_local('{vector_store_path}', embeddings, allow_dangerous_deserialization=True)")


    except Exception as e:
        logging.error(f"An error occurred in the process: {e}", exc_info=True)
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # --- Configuration ---
    markdown_folder = pathlib.Path("./docling_script_output/02_markdown_output")
    if not markdown_folder.is_dir():
        logging.error(f"Markdown folder not found: {markdown_folder}")
        print(f"Error: Markdown folder '{markdown_folder}' not found.")
        exit(1)

    # Load OpenAI API key
    api_key = load_api_key()
    if not api_key:
        print("Process aborted due to missing OpenAI API key.")
        exit(1)

    # Loop through all .md files in the folder
    for md_file in markdown_folder.glob("*.md"):
        logging.info(f"Processing file: {md_file.name}")
        # Name the vector store directory after the markdown file (without extension)
        vector_store_dir = f"faiss_index_{md_file.stem}"
        vector_store_path = str(markdown_folder.parent / vector_store_dir)
        create_embeddings_and_vector_store(
            markdown_file_path=str(md_file),
            vector_store_path=vector_store_path,
            openai_api_key=api_key
        )